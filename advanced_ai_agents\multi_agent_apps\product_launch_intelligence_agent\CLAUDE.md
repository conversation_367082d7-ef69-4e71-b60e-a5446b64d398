# CLAUDE.md
使用中文交流。

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an AI Product Launch Intelligence Agent - a Streamlit application that provides coordinated multi-agent analysis for Go-To-Market (GTM) and Product Marketing teams. The application analyzes companies across three dimensions: competitor strategy, market sentiment, and launch metrics.

## Development Commands

### Setup and Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Run the application
streamlit run product_launch_intelligence_agent.py
```

### Dependencies
- `streamlit` - Web application framework
- `agno` - Multi-agent coordination framework
- `firecrawl` - Web scraping and search API integration
- `python-dotenv` - Environment variable management

## Architecture

### Core Components

**Multi-Agent System**: The application uses three specialized AI agents that work together:
- **Product Launch Analyst** - Strategic GTM expert for competitive positioning and launch analysis
- **Market Sentiment Specialist** - Consumer perception expert for sentiment analysis
- **Launch Metrics Specialist** - Performance analytics expert for KPI tracking

**Team Coordination**: Agents are orchestrated through the `agno.Team` class in "coordinate" mode, with intelligent routing based on analysis type.

**Data Pipeline**: FirecrawlTools provide web search and crawling capabilities for all agents, with built-in retry logic and exponential backoff.

### Key Files

- `product_launch_intelligence_agent.py` - Main application file containing all logic
- `requirements.txt` - Python dependencies
- `README.md` - Project documentation

### Application Structure

**Configuration & Setup**:
- Streamlit page configuration and layout
- API key management (OpenAI and Firecrawl)
- Environment variable handling
- Agent initialization with error handling

**Agent Definitions**:
- Each agent has specialized prompts and tools
- Shared configuration (GPT-4o model, FirecrawlTools)
- Structured output requirements with source attribution

**Analysis Workflow**:
1. User enters company name
2. Selects analysis type (competitor/sentiment/metrics)
3. Appropriate agent generates bullet-point insights
4. Expansion functions create detailed reports
5. Results displayed with structured formatting

**Session State Management**:
- Separate storage for each analysis type
- Persistent results across tab switches
- Status tracking for user feedback

### API Key Management

The application supports two methods for API key configuration:
1. **Environment variables** (`.env` file): `OPENAI_API_KEY`, `FIRECRAWL_API_KEY`
2. **Sidebar inputs**: Secure password fields with fallback to environment values

### User Interface Features

**Three Analysis Tabs**:
- Competitor Analysis: Strategic positioning, strengths/weaknesses, recommendations
- Market Sentiment: Positive/negative sentiment analysis with summary
- Launch Metrics: KPI tracking with qualitative signals

**Interactive Elements**:
- Real-time status indicators
- Quick keyboard shortcuts (J/K/L)
- Expandable agent information
- Responsive layout with sidebar navigation

**Output Formatting**:
- Structured Markdown reports
- Tables for comparative analysis
- Bullet-point summaries with detailed expansions
- Source attribution for all findings

## Development Notes

- The application uses a single-file architecture for simplicity
- All agent coordination logic is contained in the main Python file
- Error handling is implemented at the agent execution level
- Session state is used to maintain analysis results across UI interactions
- The application is designed to be deployed as a standalone Streamlit app